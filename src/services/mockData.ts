// 模拟数据，用于开发测试
import type { TestResultResponse, HistoryResponse } from './api'

export const mockTestResults: TestResultResponse = {
  code: 200,
  msg: null,
  data: {
    total: 3,
    pages: 1,
    records: [
      {
        id: 1,
        historyId: 2759,
        checkUrl: "https://picsum.photos/400/300?random=1",
        req: JSON.stringify({
          inputs: {
            image: {
              transfer_method: "remote_url",
              url: "https://picsum.photos/400/300?random=1",
              type: "image"
            },
            weather: "未知"
          },
          response_mode: "blocking",
          user: "speer"
        }),
        "7bRes": null,
        "32bRes": null,
        "72bRes": JSON.stringify({
          accu: "true",
          area: 30,
          suggest: "建议立即启用应急排水设备，并设置警示标识以提醒过往行人和车辆注意安全。",
          level: "中度",
          reason: "判断依据：图片中道路低洼处可见明显积水，面积约占30%，行人需绕行，且积水已影响行人通行。积水区域主要集中在道路左侧和中间部分，深度足以影响行人通行，但尚未阻碍车辆通行。",
          alarm: 1,
          confidence: 80,
          box: [[0, 700, 400, 1148], [400, 900, 600, 1148]],
          boxFileUrl: "https://picsum.photos/400/300?random=2"
        })
      },
      {
        id: 2,
        historyId: 2760,
        checkUrl: "https://picsum.photos/400/300?random=3",
        req: JSON.stringify({
          inputs: {
            image: {
              transfer_method: "remote_url",
              url: "https://picsum.photos/400/300?random=3",
              type: "image"
            },
            weather: "晴天"
          },
          response_mode: "blocking",
          user: "speer"
        }),
        "7bRes": "无积水检测",
        "32bRes": "道路正常",
        "72bRes": JSON.stringify({
          accu: "false",
          area: 0,
          suggest: "道路状况良好，无需特殊处理。",
          level: "无",
          reason: "判断依据：图片显示道路干燥，无明显积水痕迹，路面状况良好。",
          alarm: 0,
          confidence: 95,
          box: [],
          boxFileUrl: "https://picsum.photos/400/300?random=4"
        })
      },
      {
        id: 3,
        historyId: 2761,
        checkUrl: "https://picsum.photos/400/300?random=5",
        req: JSON.stringify({
          inputs: {
            image: {
              transfer_method: "remote_url",
              url: "https://picsum.photos/400/300?random=5",
              type: "image"
            },
            weather: "雨天"
          },
          response_mode: "blocking",
          user: "speer"
        }),
        "7bRes": "检测到积水",
        "32bRes": "严重积水",
        "72bRes": JSON.stringify({
          accu: "true",
          area: 65,
          suggest: "立即封闭道路，启动应急排水系统，设置绕行路线。",
          level: "重度",
          reason: "判断依据：图片显示大面积积水，覆盖道路65%以上区域，水深较深，已严重影响交通通行。",
          alarm: 1,
          confidence: 92,
          box: [[50, 200, 350, 800], [100, 300, 400, 900]],
          boxFileUrl: "https://picsum.photos/400/300?random=6"
        })
      }
    ]
  }
}

// 巡查历史模拟数据
export const mockHistoryData: HistoryResponse = {
  code: 200,
  msg: null,
  data: {
    total: 5,
    pages: 1,
    records: [
      {
        id: 2909,
        seeperId: 5,
        seeperName: "槐安东路泵站",
        weatherData: "未知",
        taskType: "auto",
        checkTime: 1753784700000,
        fileId: "4204",
        jsStatus: "0",
        jsLevel: "无",
        suggest: "无需处置",
        resultReason: "当前图片中没有显示积水现象，路面干燥，没有明显的积水迹象。",
        areaPercentage: 0,
        isAlarm: 0,
        dealFlag: 2,
        llmRes: JSON.stringify({
          accu: false,
          area: 0,
          suggest: "无需处置",
          level: "无",
          reason: "当前图片中没有显示积水现象，路面干燥，没有明显的积水迹象。",
          alarm: 0,
          confidence: 100
        }),
        failReason: null,
        finishTime: 1753784713000,
        confidence: 100,
        isRain: 0,
        fileUrl: "https://picsum.photos/400/300?random=7"
      },
      {
        id: 2910,
        seeperId: 3,
        seeperName: "建华大街监测点",
        weatherData: "雨天",
        taskType: "manual",
        checkTime: 1753784800000,
        fileId: "4205",
        jsStatus: "1",
        jsLevel: "轻度",
        suggest: "加强监控，准备排水设备",
        resultReason: "检测到轻微积水，主要集中在道路边缘，对交通影响较小。",
        areaPercentage: 15,
        isAlarm: 0,
        dealFlag: 1,
        llmRes: JSON.stringify({
          accu: true,
          area: 15,
          suggest: "加强监控，准备排水设备",
          level: "轻度",
          reason: "检测到轻微积水，主要集中在道路边缘，对交通影响较小。",
          alarm: 0,
          confidence: 85
        }),
        failReason: null,
        finishTime: 1753784820000,
        confidence: 85,
        isRain: 1,
        fileUrl: "https://picsum.photos/400/300?random=8"
      },
      {
        id: 2911,
        seeperId: 7,
        seeperName: "体育大街泵站",
        weatherData: "大雨",
        taskType: "auto",
        checkTime: 1753784900000,
        fileId: "4206",
        jsStatus: "1",
        jsLevel: "中度",
        suggest: "立即启动排水设备，设置警示标识",
        resultReason: "道路积水面积较大，已影响正常通行，需要及时处理。",
        areaPercentage: 45,
        isAlarm: 1,
        dealFlag: 0,
        llmRes: JSON.stringify({
          accu: true,
          area: 45,
          suggest: "立即启动排水设备，设置警示标识",
          level: "中度",
          reason: "道路积水面积较大，已影响正常通行，需要及时处理。",
          alarm: 1,
          confidence: 92
        }),
        failReason: null,
        finishTime: 1753784930000,
        confidence: 92,
        isRain: 1,
        fileUrl: "https://picsum.photos/400/300?random=9"
      }
    ]
  }
}
