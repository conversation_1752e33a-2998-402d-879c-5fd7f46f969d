// 模拟数据，用于开发测试
import type { TestResultResponse } from './api'

export const mockTestResults: TestResultResponse = {
  code: 200,
  msg: null,
  data: {
    total: 3,
    pages: 1,
    records: [
      {
        id: 1,
        historyId: 2759,
        checkUrl: "https://picsum.photos/400/300?random=1",
        req: JSON.stringify({
          inputs: {
            image: {
              transfer_method: "remote_url",
              url: "https://picsum.photos/400/300?random=1",
              type: "image"
            },
            weather: "未知"
          },
          response_mode: "blocking",
          user: "speer"
        }),
        "7bRes": null,
        "32bRes": null,
        "72bRes": JSON.stringify({
          accu: "true",
          area: 30,
          suggest: "建议立即启用应急排水设备，并设置警示标识以提醒过往行人和车辆注意安全。",
          level: "中度",
          reason: "判断依据：图片中道路低洼处可见明显积水，面积约占30%，行人需绕行，且积水已影响行人通行。积水区域主要集中在道路左侧和中间部分，深度足以影响行人通行，但尚未阻碍车辆通行。",
          alarm: 1,
          confidence: 80,
          box: [[0, 700, 400, 1148], [400, 900, 600, 1148]],
          boxFileUrl: "https://picsum.photos/400/300?random=2"
        })
      },
      {
        id: 2,
        historyId: 2760,
        checkUrl: "https://picsum.photos/400/300?random=3",
        req: JSON.stringify({
          inputs: {
            image: {
              transfer_method: "remote_url",
              url: "https://picsum.photos/400/300?random=3",
              type: "image"
            },
            weather: "晴天"
          },
          response_mode: "blocking",
          user: "speer"
        }),
        "7bRes": "无积水检测",
        "32bRes": "道路正常",
        "72bRes": JSON.stringify({
          accu: "false",
          area: 0,
          suggest: "道路状况良好，无需特殊处理。",
          level: "无",
          reason: "判断依据：图片显示道路干燥，无明显积水痕迹，路面状况良好。",
          alarm: 0,
          confidence: 95,
          box: [],
          boxFileUrl: "https://picsum.photos/400/300?random=4"
        })
      },
      {
        id: 3,
        historyId: 2761,
        checkUrl: "https://picsum.photos/400/300?random=5",
        req: JSON.stringify({
          inputs: {
            image: {
              transfer_method: "remote_url",
              url: "https://picsum.photos/400/300?random=5",
              type: "image"
            },
            weather: "雨天"
          },
          response_mode: "blocking",
          user: "speer"
        }),
        "7bRes": "检测到积水",
        "32bRes": "严重积水",
        "72bRes": JSON.stringify({
          accu: "true",
          area: 65,
          suggest: "立即封闭道路，启动应急排水系统，设置绕行路线。",
          level: "重度",
          reason: "判断依据：图片显示大面积积水，覆盖道路65%以上区域，水深较深，已严重影响交通通行。",
          alarm: 1,
          confidence: 92,
          box: [[50, 200, 350, 800], [100, 300, 400, 900]],
          boxFileUrl: "https://picsum.photos/400/300?random=6"
        })
      }
    ]
  }
}
