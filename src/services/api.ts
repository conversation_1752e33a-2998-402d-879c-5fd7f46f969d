// API服务
const BASE_URL = 'http://192.168.202.212:48081/admin-api' // 请替换为实际的API地址，例如: 'http://192.168.202.212:48081/admin-api'

// 开发模式开关，设置为true时使用模拟数据
const USE_MOCK_DATA = false

// 请求配置
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer chensiming'
}

// 通用请求函数
async function request<T>(url: string, options: RequestInit = {}): Promise<T> {
  const response = await fetch(`${BASE_URL}${url}`, {
    headers: {
      ...defaultHeaders,
      ...options.headers
    },
    ...options
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return response.json()
}

// 测试结果相关接口
export interface TestRecord {
  id: number
  historyId: number
  checkUrl: string
  req: string
  '7bRes': string | null
  '32bRes': string | null
  '72bRes': string | null
}

export interface TestResultResponse {
  code: number
  msg: string | null
  data: {
    total: number
    pages: number
    records: TestRecord[]
  }
}

export interface TestResultParams {
  pageSize: number
  pageNum: number
}

export interface HistoryParams extends TestResultParams {
  jsStatus?: string // 积水状态筛选 0-正常 1-积水
  jsLevel?: string  // 严重程度筛选
  id?: number       // 查询特定ID的历史记录
}

// 获取测试结果记录
export async function getTestResults(params: TestResultParams): Promise<TestResultResponse> {
  if (USE_MOCK_DATA) {
    // 使用模拟数据
    const { mockTestResults } = await import('./mockData')
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    return mockTestResults
  }

  return request<TestResultResponse>('/biz/seeper-test/list', {
    method: 'POST',
    body: JSON.stringify(params)
  })
}

// 重新测试响应接口
export interface RetestResponse {
  code: number
  msg: string | null
  data?: unknown
}

// 发起再次测试
export async function retestByHistoryId(historyId: number): Promise<RetestResponse> {
  if (USE_MOCK_DATA) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    return {
      code: 200,
      msg: '重新测试已发起',
      data: { historyId }
    }
  }

  return request<RetestResponse>(`/biz/seeper-test/run?hisId=${historyId}`, {
    method: 'POST'
  })
}

// 解析72b模型结果
export interface ModelResult {
  accu: string // 是否积水
  area: number // 积水面积
  suggest: string // 积水建议
  level: string // 严重程度
  reason: string // 判断依据
  alarm: number // 是否告警
  confidence: number // 置信度
  box: number[][] // 边框坐标
  boxFileUrl: string // 边框绘制后的图片地址
}

export function parseModelResult(resultStr: string | null): ModelResult | null {
  if (!resultStr) return null
  try {
    return JSON.parse(resultStr)
  } catch (error) {
    console.error('解析模型结果失败:', error)
    return null
  }
}

// 巡查历史相关接口
export interface HistoryRecord {
  id: number // 巡查id
  seeperId: number
  seeperName: string // 巡查点名称
  weatherData: string
  taskType: string
  checkTime: number
  fileId: string
  jsStatus: string
  jsLevel: string
  suggest: string
  resultReason: string
  areaPercentage: number
  isAlarm: number
  dealFlag: number
  llmRes: string
  failReason: string | null
  finishTime: number
  confidence: number
  isRain: number
  fileUrl: string
}

export interface HistoryResponse {
  code: number
  msg: string | null
  data: {
    total: number
    pages: number
    records: HistoryRecord[]
  }
}

// 获取巡查历史列表
export async function getHistoryList(params: HistoryParams): Promise<HistoryResponse> {
  if (USE_MOCK_DATA) {
    // 使用模拟数据
    const { mockHistoryData } = await import('./mockData')
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    return mockHistoryData
  }

  return request<HistoryResponse>('/biz/seeper-history/list', {
    method: 'POST',
    body: JSON.stringify(params)
  })
}
