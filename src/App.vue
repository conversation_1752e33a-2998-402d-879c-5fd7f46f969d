<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const selectedKeys = ref([route.name as string])

// 监听路由变化
router.afterEach((to) => {
  selectedKeys.value = [to.name as string]
})

const menuItems = [
  {
    key: 'home',
    label: '首页',
    path: '/'
  },
  {
    key: 'test-result',
    label: '测试结果',
    path: '/test-result'
  }
]

const handleMenuClick = ({ key }: { key: string }) => {
  const item = menuItems.find(item => item.key === key)
  if (item) {
    router.push(item.path)
  }
}
</script>

<template>
  <a-layout style="min-height: 100vh">
    <a-layout-header style="background: #fff; padding: 0 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)">
      <div style="display: flex; align-items: center; height: 64px;">
        <div style="font-size: 20px; font-weight: bold; margin-right: 40px; color: #1890ff;">
          积水检测系统
        </div>
        <a-menu
          mode="horizontal"
          :selected-keys="selectedKeys"
          @click="handleMenuClick"
          style="border-bottom: none; flex: 1;"
        >
          <a-menu-item v-for="item in menuItems" :key="item.key">
            {{ item.label }}
          </a-menu-item>
        </a-menu>
      </div>
    </a-layout-header>

    <a-layout-content style="background: #f0f2f5;">
      <RouterView />
    </a-layout-content>
  </a-layout>
</template>

<style>
body {
  margin: 0;
  padding: 0;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}
</style>
