<template>
  <div class="test-result-container" :class="{ 'global-loading': globalLoading }">
    <div class="header">
      <h1>积水测试结果</h1>
    </div>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading || globalLoading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :scroll="{ x: 1650 }"
    >
      <!-- 检测图片列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'checkUrl'">
          <a-image
            :src="record.checkUrl"
            :width="100"
            :height="80"
            style="object-fit: cover"
            :preview="true"
          />
        </template>

        <!-- 请求信息列 -->
        <template v-else-if="column.key === 'req'">
          <a-button type="link" @click="showRequestDetail(record.req)">
            查看详情
          </a-button>
        </template>

        <!-- 7B模型结果列 -->
        <template v-else-if="column.key === '7bRes'">
          <div v-if="record['7bRes']">
            <div class="model-result-summary">
              <div v-if="parseModelResult(record['7bRes'])">
                <a-tag :color="parseModelResult(record['7bRes'])?.accu === 'true' ? 'red' : 'green'" size="small">
                  {{ parseModelResult(record['7bRes'])?.accu === 'true' ? '积水' : '正常' }}
                </a-tag>
                <a-tag :color="getLevelColor(parseModelResult(record['7bRes'])?.level || '')" size="small">
                  {{ parseModelResult(record['7bRes'])?.level || '未知' }}
                </a-tag>
              </div>
              <div v-else>
                <span class="simple-result">{{ record['7bRes'] }}</span>
              </div>
              <a-button type="link" size="small" @click="showModelResult(record['7bRes'], '7B')">
                详情
              </a-button>
            </div>
          </div>
          <span v-else class="no-result">暂无结果</span>
        </template>

        <!-- 9B模型结果列 -->
        <template v-else-if="column.key === '9bRes'">
          <div v-if="record['9bRes']">
            <div class="model-result-summary">
              <div v-if="parseModelResult(record['9bRes'])">
                <a-tag :color="parseModelResult(record['9bRes'])?.accu === 'true' ? 'red' : 'green'" size="small">
                  {{ parseModelResult(record['9bRes'])?.accu === 'true' ? '积水' : '正常' }}
                </a-tag>
                <a-tag :color="getLevelColor(parseModelResult(record['9bRes'])?.level || '')" size="small">
                  {{ parseModelResult(record['9bRes'])?.level || '未知' }}
                </a-tag>
              </div>
              <div v-else>
                <span class="simple-result">{{ record['9bRes'] }}</span>
              </div>
              <a-button type="link" size="small" @click="showModelResult(record['9bRes'], '9B')">
                详情
              </a-button>
            </div>
          </div>
          <span v-else class="no-result">暂无结果</span>
        </template>

        <!-- 32B模型结果列 -->
        <template v-else-if="column.key === '32bRes'">
          <div v-if="record['32bRes']">
            <div class="model-result-summary">
              <div v-if="parseModelResult(record['32bRes'])">
                <a-tag :color="parseModelResult(record['32bRes'])?.accu === 'true' ? 'red' : 'green'" size="small">
                  {{ parseModelResult(record['32bRes'])?.accu === 'true' ? '积水' : '正常' }}
                </a-tag>
                <a-tag :color="getLevelColor(parseModelResult(record['32bRes'])?.level || '')" size="small">
                  {{ parseModelResult(record['32bRes'])?.level || '未知' }}
                </a-tag>
              </div>
              <div v-else>
                <span class="simple-result">{{ record['32bRes'] }}</span>
              </div>
              <a-button type="link" size="small" @click="showModelResult(record['32bRes'], '32B')">
                详情
              </a-button>
            </div>
          </div>
          <span v-else class="no-result">暂无结果</span>
        </template>

        <!-- 72B模型结果列 -->
        <template v-else-if="column.key === '72bRes'">
          <div v-if="record['72bRes']">
            <div class="model-result-summary">
              <div v-if="parseModelResult(record['72bRes'])">
                <a-tag :color="parseModelResult(record['72bRes'])?.accu === 'true' ? 'red' : 'green'" size="small">
                  {{ parseModelResult(record['72bRes'])?.accu === 'true' ? '积水' : '正常' }}
                </a-tag>
                <a-tag :color="getLevelColor(parseModelResult(record['72bRes'])?.level || '')" size="small">
                  {{ parseModelResult(record['72bRes'])?.level || '未知' }}
                </a-tag>
              </div>
              <div v-else>
                <span class="simple-result">{{ record['72bRes'] }}</span>
              </div>
              <a-button type="link" size="small" @click="showModelResult(record['72bRes'], '72B')">
                详情
              </a-button>
            </div>
          </div>
          <span v-else class="no-result">暂无结果</span>
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-space direction="vertical" size="small">
            <a-button
              type="primary"
              size="small"
              @click="showRetestModal(record.historyId)"
              :disabled="globalLoading"
            >
              重新测试
            </a-button>
            <a-button
              size="small"
              @click="handleQueryHistory(record.historyId)"
              :loading="queryHistoryLoading[record.id]"
              :disabled="globalLoading"
            >
              查询关联历史
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 请求详情弹窗 -->
    <a-modal
      v-model:open="requestDetailVisible"
      title="请求详情"
      :footer="null"
      width="800px"
    >
      <pre class="request-detail">{{ formatJson(currentRequestDetail) }}</pre>
    </a-modal>

    <!-- 模型结果弹窗 -->
    <a-modal
      v-model:open="modelResultVisible"
      :title="`${currentModelType}模型检测结果`"
      :footer="null"
      width="1000px"
    >
      <div v-if="currentModelResult" class="model-result">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="检测结果" size="small">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="是否积水">
                  <a-tag :color="currentModelResult.accu === 'true' ? 'red' : 'green'">
                    {{ currentModelResult.accu === 'true' ? '是' : '否' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="积水面积">
                  {{ currentModelResult.area }}%
                </a-descriptions-item>
                <a-descriptions-item label="严重程度">
                  <a-tag :color="getLevelColor(currentModelResult.level)">
                    {{ currentModelResult.level }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="是否告警">
                  <a-tag :color="currentModelResult.alarm === 1 ? 'red' : 'green'">
                    {{ currentModelResult.alarm === 1 ? '是' : '否' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="置信度">
                  {{ currentModelResult.confidence }}%
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="边框标注图片" size="small">
              <a-image
                :src="currentModelResult.boxFileUrl"
                :width="300"
                style="max-width: 100%"
                :preview="true"
              />
            </a-card>
          </a-col>
        </a-row>
        
        <a-card title="判断依据" size="small" style="margin-top: 16px;">
          <p>{{ currentModelResult.reason }}</p>
        </a-card>
        
        <a-card title="处理建议" size="small" style="margin-top: 16px;">
          <p>{{ currentModelResult.suggest }}</p>
        </a-card>
      </div>
    </a-modal>

    <!-- 重新测试选择弹窗 -->
    <a-modal
      v-model:open="retestModalVisible"
      title="选择测试类型和模型"
      :footer="null"
      width="500px"
      :mask-closable="false"
      :closable="!globalLoading"
    >
      <div style="padding: 20px 0;">
        <a-form layout="vertical">
          <a-form-item label="测试类型" style="margin-bottom: 24px;">
            <a-radio-group v-model:value="selectedTestType" style="width: 100%;">
              <a-space direction="vertical" style="width: 100%;">
                <a-radio value="new">新发起测试</a-radio>
                <a-radio value="update">更新测试</a-radio>
              </a-space>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="选择模型" style="margin-bottom: 24px;">
            <a-select v-model:value="selectedModel" style="width: 100%;" size="large">
              <a-select-option value="7b">7B模型</a-select-option>
              <a-select-option value="9b">9B模型</a-select-option>
              <a-select-option value="32b">32B模型</a-select-option>
              <a-select-option value="72b">72B模型</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item style="margin-bottom: 0; text-align: center;">
            <a-space size="large">
              <a-button
                type="primary"
                size="large"
                @click="handleRetest()"
                :loading="globalLoading"
              >
                开始测试
              </a-button>
              <a-button
                size="large"
                @click="retestModalVisible = false"
                :disabled="globalLoading"
              >
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 关联历史弹窗 -->
    <a-modal
      v-model:open="historyModalVisible"
      title="关联历史记录"
      :footer="null"
      width="1200px"
      :mask-closable="!globalLoading"
      :closable="!globalLoading"
    >
      <a-table
        :columns="historyColumns"
        :data-source="historyDataSource"
        :loading="historyLoading"
        :pagination="false"
        row-key="id"
        size="small"
        :scroll="{ x: 1000 }"
      >
        <!-- 巡查图片列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'fileUrl'">
            <a-image
              :src="record.fileUrl"
              :width="60"
              :height="45"
              style="object-fit: cover"
              :preview="true"
            />
          </template>

          <!-- 巡查时间列 -->
          <template v-else-if="column.key === 'checkTime'">
            {{ formatTime(record.checkTime) }}
          </template>

          <!-- 积水状态列 -->
          <template v-else-if="column.key === 'jsStatus'">
            <a-tag :color="record.jsStatus === '1' ? 'red' : 'green'" size="small">
              {{ record.jsStatus === '1' ? '积水' : '正常' }}
            </a-tag>
          </template>

          <!-- 积水等级列 -->
          <template v-else-if="column.key === 'jsLevel'">
            <a-tag :color="getLevelColor(record.jsLevel)" size="small">
              {{ record.jsLevel }}
            </a-tag>
          </template>

          <!-- 是否告警列 -->
          <template v-else-if="column.key === 'isAlarm'">
            <a-tag :color="record.isAlarm === 1 ? 'red' : 'green'" size="small">
              {{ record.isAlarm === 1 ? '告警' : '正常' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { getTestResults, retestByHistoryId, parseModelResult, getHistoryList, type TestRecord, type ModelResult, type HistoryRecord } from '@/services/api'

// 响应式数据
const loading = ref(false)
const dataSource = ref<TestRecord[]>([])
const requestDetailVisible = ref(false)
const modelResultVisible = ref(false)
const currentRequestDetail = ref('')
const currentModelResult = ref<ModelResult | null>(null)
const currentModelType = ref('72B')
const queryHistoryLoading = reactive<Record<number, boolean>>({})

// 重新测试相关
const retestModalVisible = ref(false)
const globalLoading = ref(false)
const currentHistoryId = ref<number | null>(null)
const selectedTestType = ref<'new' | 'update'>('new')
const selectedModel = ref('72b')

// 关联历史相关
const historyModalVisible = ref(false)
const historyLoading = ref(false)
const historyDataSource = ref<HistoryRecord[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '历史ID',
    dataIndex: 'historyId',
    key: 'historyId',
    width: 100
  },
  {
    title: '检测图片',
    dataIndex: 'checkUrl',
    key: 'checkUrl',
    width: 120
  },
  {
    title: '请求信息',
    dataIndex: 'req',
    key: 'req',
    width: 100
  },
  {
    title: '7B结果',
    dataIndex: '7bRes',
    key: '7bRes',
    width: 150
  },
  {
    title: '9B结果',
    dataIndex: '9bRes',
    key: '9bRes',
    width: 150
  },
  {
    title: '32B结果',
    dataIndex: '32bRes',
    key: '32bRes',
    width: 150
  },
  {
    title: '72B结果',
    dataIndex: '72bRes',
    key: '72bRes',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 关联历史表格列配置
const historyColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '巡查点名称',
    dataIndex: 'seeperName',
    key: 'seeperName',
    width: 120
  },
  {
    title: '巡查图片',
    dataIndex: 'fileUrl',
    key: 'fileUrl',
    width: 100
  },
  {
    title: '巡查时间',
    dataIndex: 'checkTime',
    key: 'checkTime',
    width: 150
  },
  {
    title: '积水状态',
    dataIndex: 'jsStatus',
    key: 'jsStatus',
    width: 100
  },
  {
    title: '积水等级',
    dataIndex: 'jsLevel',
    key: 'jsLevel',
    width: 100
  },
  {
    title: '积水面积',
    dataIndex: 'areaPercentage',
    key: 'areaPercentage',
    width: 100,
    customRender: ({ text }: { text: number }) => `${text}%`
  },
  {
    title: '是否告警',
    dataIndex: 'isAlarm',
    key: 'isAlarm',
    width: 100
  },
  {
    title: '置信度',
    dataIndex: 'confidence',
    key: 'confidence',
    width: 100,
    customRender: ({ text }: { text: number }) => `${text}%`
  }
]

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getTestResults({
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    })
    
    if (response.code === 200) {
      dataSource.value = response.data.records
      pagination.total = response.data.total
    } else {
      message.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    message.error('网络请求失败')
    console.error('获取测试结果失败:', error)
  } finally {
    loading.value = false
  }
}

// 表格变化处理
const handleTableChange = (pag: { current: number; pageSize: number }) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchData()
}

// 显示重新测试弹窗
const showRetestModal = (historyId: number) => {
  currentHistoryId.value = historyId
  retestModalVisible.value = true
}

// 处理重新测试
const handleRetest = async () => {
  if (!currentHistoryId.value) return

  globalLoading.value = true

  try {
    await retestByHistoryId(currentHistoryId.value, selectedModel.value)
    message.success(`${selectedTestType.value === 'new' ? '新发起测试' : '更新测试'}已发起，请稍后查看结果`)
    retestModalVisible.value = false
    await fetchData() // 重新获取数据
  } catch (error) {
    message.error(`${selectedTestType.value === 'new' ? '新发起测试' : '更新测试'}失败`)
    console.error('重新测试失败:', error)
  } finally {
    globalLoading.value = false
    currentHistoryId.value = null
    // 重置选择
    selectedTestType.value = 'new'
    selectedModel.value = '72b'
  }
}

// 显示请求详情
const showRequestDetail = (req: string) => {
  currentRequestDetail.value = req
  requestDetailVisible.value = true
}

// 显示模型结果
const showModelResult = (resultStr: string, modelType: string = '72B') => {
  const result = parseModelResult(resultStr)
  if (result) {
    currentModelResult.value = result
    currentModelType.value = modelType
    modelResultVisible.value = true
  } else {
    // 如果不是JSON格式，直接显示文本
    currentModelResult.value = {
      accu: 'unknown',
      area: 0,
      suggest: resultStr,
      level: '未知',
      reason: resultStr,
      alarm: 0,
      confidence: 0,
      box: [],
      boxFileUrl: ''
    }
    currentModelType.value = modelType
    modelResultVisible.value = true
  }
}

// 格式化JSON
const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 查询关联历史
const handleQueryHistory = async (historyId: number) => {
  const recordId = dataSource.value.find(item => item.historyId === historyId)?.id
  if (!recordId) return

  queryHistoryLoading[recordId] = true
  historyLoading.value = true

  try {
    const response = await getHistoryList({
      pageNum: 1,
      pageSize: 100,
      id: historyId
    })

    if (response.code === 200) {
      historyDataSource.value = response.data.records
      historyModalVisible.value = true
    } else {
      message.error(response.msg || '查询关联历史失败')
    }
  } catch (error) {
    message.error('查询关联历史失败')
    console.error('查询关联历史失败:', error)
  } finally {
    queryHistoryLoading[recordId] = false
    historyLoading.value = false
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取严重程度颜色
const getLevelColor = (level: string) => {
  switch (level) {
    case '无': return 'default'
    case '轻微': return 'cyan'
    case '轻度': return 'green'
    case '中度': return 'orange'
    case '严重': return 'red'
    case '重度': return 'red'
    case '非常严重': return 'magenta'
    default: return 'default'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.test-result-container {
  padding: 24px;
}

.header {
  margin-bottom: 24px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.request-detail {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.model-result {
  max-height: 600px;
  overflow-y: auto;
}

.no-result {
  color: #999;
  font-style: italic;
}

.model-result-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.model-result-summary > div {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.simple-result {
  font-size: 12px;
  color: #666;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.global-loading {
  position: relative;
  pointer-events: none;
}

.global-loading::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 1000;
  pointer-events: auto;
}

.global-loading .ant-modal {
  pointer-events: auto;
}
</style>
