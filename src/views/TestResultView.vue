<template>
  <div class="test-result-container">
    <div class="header">
      <h1>积水测试结果</h1>
      <a-button type="primary" @click="handleRetest" :loading="retestLoading">
        再次测试
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :scroll="{ x: 1500 }"
    >
      <!-- 检测图片列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'checkUrl'">
          <a-image
            :src="record.checkUrl"
            :width="100"
            :height="80"
            style="object-fit: cover"
            :preview="true"
          />
        </template>

        <!-- 请求信息列 -->
        <template v-else-if="column.key === 'req'">
          <a-button type="link" @click="showRequestDetail(record.req)">
            查看详情
          </a-button>
        </template>

        <!-- 72b模型结果列 -->
        <template v-else-if="column.key === '72bRes'">
          <div v-if="parseModelResult(record['72bRes'])">
            <a-button type="link" @click="showModelResult(record['72bRes'])">
              查看结果
            </a-button>
          </div>
          <span v-else class="no-result">暂无结果</span>
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-button 
            type="primary" 
            size="small" 
            @click="handleSingleRetest(record.historyId)"
            :loading="singleRetestLoading[record.id]"
          >
            重新测试
          </a-button>
        </template>
      </template>
    </a-table>

    <!-- 请求详情弹窗 -->
    <a-modal
      v-model:open="requestDetailVisible"
      title="请求详情"
      :footer="null"
      width="800px"
    >
      <pre class="request-detail">{{ formatJson(currentRequestDetail) }}</pre>
    </a-modal>

    <!-- 模型结果弹窗 -->
    <a-modal
      v-model:open="modelResultVisible"
      title="72B模型检测结果"
      :footer="null"
      width="1000px"
    >
      <div v-if="currentModelResult" class="model-result">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="检测结果" size="small">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="是否积水">
                  <a-tag :color="currentModelResult.accu === 'true' ? 'red' : 'green'">
                    {{ currentModelResult.accu === 'true' ? '是' : '否' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="积水面积">
                  {{ currentModelResult.area }}%
                </a-descriptions-item>
                <a-descriptions-item label="严重程度">
                  <a-tag :color="getLevelColor(currentModelResult.level)">
                    {{ currentModelResult.level }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="是否告警">
                  <a-tag :color="currentModelResult.alarm === 1 ? 'red' : 'green'">
                    {{ currentModelResult.alarm === 1 ? '是' : '否' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="置信度">
                  {{ currentModelResult.confidence }}%
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="边框标注图片" size="small">
              <a-image
                :src="currentModelResult.boxFileUrl"
                :width="300"
                style="max-width: 100%"
                :preview="true"
              />
            </a-card>
          </a-col>
        </a-row>
        
        <a-card title="判断依据" size="small" style="margin-top: 16px;">
          <p>{{ currentModelResult.reason }}</p>
        </a-card>
        
        <a-card title="处理建议" size="small" style="margin-top: 16px;">
          <p>{{ currentModelResult.suggest }}</p>
        </a-card>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { getTestResults, retestByHistoryId, parseModelResult, type TestRecord, type ModelResult } from '@/services/api'

// 响应式数据
const loading = ref(false)
const retestLoading = ref(false)
const dataSource = ref<TestRecord[]>([])
const requestDetailVisible = ref(false)
const modelResultVisible = ref(false)
const currentRequestDetail = ref('')
const currentModelResult = ref<ModelResult | null>(null)
const singleRetestLoading = reactive<Record<number, boolean>>({})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '历史ID',
    dataIndex: 'historyId',
    key: 'historyId',
    width: 100
  },
  {
    title: '检测图片',
    dataIndex: 'checkUrl',
    key: 'checkUrl',
    width: 120
  },
  {
    title: '请求信息',
    dataIndex: 'req',
    key: 'req',
    width: 100
  },
  {
    title: '7B结果',
    dataIndex: '7bRes',
    key: '7bRes',
    width: 100,
    customRender: ({ text }: { text: string | null }) => text || '暂无结果'
  },
  {
    title: '32B结果',
    dataIndex: '32bRes',
    key: '32bRes',
    width: 100,
    customRender: ({ text }: { text: string | null }) => text || '暂无结果'
  },
  {
    title: '72B结果',
    dataIndex: '72bRes',
    key: '72bRes',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getTestResults({
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    })
    
    if (response.code === 200) {
      dataSource.value = response.data.records
      pagination.total = response.data.total
    } else {
      message.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    message.error('网络请求失败')
    console.error('获取测试结果失败:', error)
  } finally {
    loading.value = false
  }
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchData()
}

// 再次测试
const handleRetest = async () => {
  retestLoading.value = true
  try {
    // 这里需要根据实际需求实现再次测试的逻辑
    // 可能需要传入特定的参数或者调用不同的接口
    message.success('测试已发起，请稍后查看结果')
    await fetchData() // 重新获取数据
  } catch (error) {
    message.error('发起测试失败')
    console.error('再次测试失败:', error)
  } finally {
    retestLoading.value = false
  }
}

// 单个记录重新测试
const handleSingleRetest = async (historyId: number) => {
  const recordId = dataSource.value.find(item => item.historyId === historyId)?.id
  if (!recordId) return
  
  singleRetestLoading[recordId] = true
  try {
    await retestByHistoryId(historyId)
    message.success('重新测试已发起，请稍后查看结果')
    await fetchData() // 重新获取数据
  } catch (error) {
    message.error('重新测试失败')
    console.error('重新测试失败:', error)
  } finally {
    singleRetestLoading[recordId] = false
  }
}

// 显示请求详情
const showRequestDetail = (req: string) => {
  currentRequestDetail.value = req
  requestDetailVisible.value = true
}

// 显示模型结果
const showModelResult = (resultStr: string) => {
  const result = parseModelResult(resultStr)
  if (result) {
    currentModelResult.value = result
    modelResultVisible.value = true
  } else {
    message.error('解析模型结果失败')
  }
}

// 格式化JSON
const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 获取严重程度颜色
const getLevelColor = (level: string) => {
  switch (level) {
    case '轻度': return 'green'
    case '中度': return 'orange'
    case '重度': return 'red'
    default: return 'default'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.test-result-container {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.request-detail {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.model-result {
  max-height: 600px;
  overflow-y: auto;
}

.no-result {
  color: #999;
  font-style: italic;
}
</style>
