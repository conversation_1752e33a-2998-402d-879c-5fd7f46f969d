<template>
  <div class="history-container">
    <div class="header">
      <h1>巡查历史</h1>
    </div>

    <!-- 筛选条件 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="积水状态">
            <a-select
              v-model:value="filters.jsStatus"
              placeholder="请选择积水状态"
              allowClear
              @change="handleFilterChange"
            >
              <a-select-option value="0">正常</a-select-option>
              <a-select-option value="1">积水</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="严重程度">
            <a-select
              v-model:value="filters.jsLevel"
              placeholder="请选择严重程度"
              allowClear
              @change="handleFilterChange"
            >
              <a-select-option value="无">无</a-select-option>
              <a-select-option value="轻微">轻微</a-select-option>
              <a-select-option value="轻度">轻度</a-select-option>
              <a-select-option value="中度">中度</a-select-option>
              <a-select-option value="严重">严重</a-select-option>
              <a-select-option value="非常严重">非常严重</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label=" ">
            <a-space>
              <a-button type="primary" @click="handleSearch" :loading="loading">
                查询
              </a-button>
              <a-button @click="handleReset">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
      :scroll="{ x: 1800 }"
    >
      <!-- 巡查点名称列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'seeperName'">
          <a-tag color="blue">{{ record.seeperName }}</a-tag>
        </template>

        <!-- 巡查图片列 -->
        <template v-else-if="column.key === 'fileUrl'">
          <a-image
            :src="record.fileUrl"
            :width="100"
            :height="80"
            style="object-fit: cover"
            :preview="true"
          />
        </template>

        <!-- 巡查时间列 -->
        <template v-else-if="column.key === 'checkTime'">
          {{ formatTime(record.checkTime) }}
        </template>

        <!-- 完成时间列 -->
        <template v-else-if="column.key === 'finishTime'">
          {{ formatTime(record.finishTime) }}
        </template>

        <!-- 积水状态列 -->
        <template v-else-if="column.key === 'jsStatus'">
          <a-tag :color="record.jsStatus === '1' ? 'red' : 'green'">
            {{ record.jsStatus === '1' ? '有积水' : '无积水' }}
          </a-tag>
        </template>

        <!-- 积水等级列 -->
        <template v-else-if="column.key === 'jsLevel'">
          <a-tag :color="getLevelColor(record.jsLevel)">
            {{ record.jsLevel }}
          </a-tag>
        </template>

        <!-- 是否告警列 -->
        <template v-else-if="column.key === 'isAlarm'">
          <a-tag :color="record.isAlarm === 1 ? 'red' : 'green'">
            {{ record.isAlarm === 1 ? '告警' : '正常' }}
          </a-tag>
        </template>

        <!-- 处理状态列 -->
        <template v-else-if="column.key === 'dealFlag'">
          <a-tag :color="getDealFlagColor(record.dealFlag)">
            {{ getDealFlagText(record.dealFlag) }}
          </a-tag>
        </template>

        <!-- LLM结果列 -->
        <template v-else-if="column.key === 'llmRes'">
          <a-button type="link" @click="showLlmResult(record.llmRes)">
            查看详情
          </a-button>
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <a-button 
            type="primary" 
            size="small" 
            @click="handleTest(record.id)"
            :loading="testLoading[record.id]"
          >
            发起测试
          </a-button>
        </template>
      </template>
    </a-table>

    <!-- LLM结果详情弹窗 -->
    <a-modal
      v-model:open="llmResultVisible"
      title="LLM检测结果详情"
      :footer="null"
      width="800px"
    >
      <div v-if="currentLlmResult" class="llm-result">
        <a-descriptions :column="2" size="small" bordered>
          <a-descriptions-item label="是否积水">
            <a-tag :color="currentLlmResult.accu ? 'red' : 'green'">
              {{ currentLlmResult.accu ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="积水面积">
            {{ currentLlmResult.area }}%
          </a-descriptions-item>
          <a-descriptions-item label="严重程度">
            <a-tag :color="getLevelColor(currentLlmResult.level)">
              {{ currentLlmResult.level }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="是否告警">
            <a-tag :color="currentLlmResult.alarm === 1 ? 'red' : 'green'">
              {{ currentLlmResult.alarm === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="置信度" :span="2">
            <a-progress :percent="currentLlmResult.confidence" size="small" />
          </a-descriptions-item>
        </a-descriptions>
        
        <a-card title="判断依据" size="small" style="margin-top: 16px;">
          <p>{{ currentLlmResult.reason }}</p>
        </a-card>
        
        <a-card title="处理建议" size="small" style="margin-top: 16px;">
          <p>{{ currentLlmResult.suggest }}</p>
        </a-card>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { getHistoryList, retestByHistoryId, type HistoryRecord, type HistoryParams } from '@/services/api'

// 响应式数据
const loading = ref(false)
const dataSource = ref<HistoryRecord[]>([])
const llmResultVisible = ref(false)
const currentLlmResult = ref<any>(null)
const testLoading = reactive<Record<number, boolean>>({})

// 筛选条件
const filters = reactive({
  jsStatus: undefined as string | undefined,
  jsLevel: undefined as string | undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '巡查点名称',
    dataIndex: 'seeperName',
    key: 'seeperName',
    width: 150
  },
  {
    title: '巡查图片',
    dataIndex: 'fileUrl',
    key: 'fileUrl',
    width: 120
  },
  {
    title: '巡查时间',
    dataIndex: 'checkTime',
    key: 'checkTime',
    width: 150
  },
  {
    title: '完成时间',
    dataIndex: 'finishTime',
    key: 'finishTime',
    width: 150
  },
  {
    title: '积水状态',
    dataIndex: 'jsStatus',
    key: 'jsStatus',
    width: 100
  },
  {
    title: '积水等级',
    dataIndex: 'jsLevel',
    key: 'jsLevel',
    width: 100
  },
  {
    title: '积水面积',
    dataIndex: 'areaPercentage',
    key: 'areaPercentage',
    width: 100,
    customRender: ({ text }: { text: number }) => `${text}%`
  },
  {
    title: '是否告警',
    dataIndex: 'isAlarm',
    key: 'isAlarm',
    width: 100
  },
  {
    title: '置信度',
    dataIndex: 'confidence',
    key: 'confidence',
    width: 100,
    customRender: ({ text }: { text: number }) => `${text}%`
  },
  {
    title: '处理状态',
    dataIndex: 'dealFlag',
    key: 'dealFlag',
    width: 100
  },
  {
    title: 'LLM结果',
    dataIndex: 'llmRes',
    key: 'llmRes',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: HistoryParams = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }

    // 添加筛选条件
    if (filters.jsStatus !== undefined) {
      params.jsStatus = filters.jsStatus
    }
    if (filters.jsLevel !== undefined) {
      params.jsLevel = filters.jsLevel
    }

    const response = await getHistoryList(params)
    
    if (response.code === 200) {
      dataSource.value = response.data.records
      pagination.total = response.data.total
    } else {
      message.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    message.error('网络请求失败')
    console.error('获取巡查历史失败:', error)
  } finally {
    loading.value = false
  }
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchData()
}

// 筛选条件变化处理
const handleFilterChange = () => {
  // 筛选条件变化时重置到第一页
  pagination.current = 1
}

// 查询按钮处理
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置按钮处理
const handleReset = () => {
  filters.jsStatus = undefined
  filters.jsLevel = undefined
  pagination.current = 1
  fetchData()
}

// 发起测试
const handleTest = async (historyId: number) => {
  testLoading[historyId] = true
  try {
    await retestByHistoryId(historyId)
    message.success('测试已发起，请稍后查看结果')
  } catch (error) {
    message.error('发起测试失败')
    console.error('发起测试失败:', error)
  } finally {
    testLoading[historyId] = false
  }
}

// 显示LLM结果
const showLlmResult = (llmResStr: string) => {
  try {
    currentLlmResult.value = JSON.parse(llmResStr)
    llmResultVisible.value = true
  } catch (error) {
    message.error('解析LLM结果失败')
    console.error('解析LLM结果失败:', error)
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取等级颜色
const getLevelColor = (level: string) => {
  switch (level) {
    case '无': return 'default'
    case '轻微': return 'cyan'
    case '轻度': return 'green'
    case '中度': return 'orange'
    case '严重': return 'red'
    case '非常严重': return 'magenta'
    default: return 'default'
  }
}

// 获取处理状态颜色
const getDealFlagColor = (flag: number) => {
  switch (flag) {
    case 0: return 'red'      // 未处理
    case 1: return 'orange'   // 处理中
    case 2: return 'green'    // 已处理
    default: return 'default'
  }
}

// 获取处理状态文本
const getDealFlagText = (flag: number) => {
  switch (flag) {
    case 0: return '未处理'
    case 1: return '处理中'
    case 2: return '已处理'
    default: return '未知'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.history-container {
  padding: 24px;
}

.header {
  margin-bottom: 24px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.llm-result {
  max-height: 600px;
  overflow-y: auto;
}
</style>
