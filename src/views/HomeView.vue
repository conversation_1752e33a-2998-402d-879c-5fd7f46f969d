<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToTestResult = () => {
  router.push('/test-result')
}
</script>

<template>
  <div class="home-container">
    <a-card style="max-width: 600px; margin: 40px auto;">
      <template #title>
        <div style="text-align: center;">
          <h1 style="margin: 0; color: #1890ff;">积水检测系统</h1>
        </div>
      </template>

      <div style="text-align: center; padding: 40px 0;">
        <p style="font-size: 16px; color: #666; margin-bottom: 40px;">
          欢迎使用积水检测系统，该系统可以通过AI模型检测道路积水情况
        </p>

        <a-space size="large">
          <a-button type="primary" size="large" @click="goToTestResult">
            查看测试结果
          </a-button>
          <a-button size="large" disabled>
            巡查历史（待开发）
          </a-button>
        </a-space>
      </div>
    </a-card>
  </div>
</template>

<style scoped>
.home-container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}
</style>
